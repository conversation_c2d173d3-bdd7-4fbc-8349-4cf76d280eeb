<script setup lang="ts">
import { computed, ref, watch } from 'vue';

interface DeviceType {
  id: string;
  name: string;
  path: string;
  scale: number;
  defaultMaterial: {
    color: number;
    roughness: number;
    metalness: number;
  };
}

interface DeviceConfig {
  id: string;
  name: string;
  type: string;
  path: string;
  position: { x: number; y: number; z: number };
  rotation: { x: number; y: number; z: number };
  scale: number;
  material: {
    color: number;
    roughness: number;
    metalness: number;
  };
  battery?: number;
  sensors?: any[];
}

interface Props {
  sceneManagerRef?: any;
  models: any[];
}

const props = defineProps<Props>();

const emit = defineEmits(['device-added', 'device-removed', 'devices-imported', 'devices-exported']);

// 七种车型配置
const deviceTypes: DeviceType[] = [
  {
    id: 'bridge_loader',
    name: '桥式转载机',
    path: '/models/桥式转载机ok.glb',
    scale: 0.005,
    defaultMaterial: { color: 0x6c0202, roughness: 0.2, metalness: 0.9 }
  },
  {
    id: 'large_spreader',
    name: '大型布料机',
    path: '/models/大型布料机ok-6.glb',
    scale: 0.0088,
    defaultMaterial: { color: 0x02536c, roughness: 0.2, metalness: 0.9 }
  },
  {
    id: 'relay_loader',
    name: '中继转载机',
    path: '/models/中继转载机-B-ok.glb',
    scale: 0.01,
    defaultMaterial: { color: 0x3a6c02, roughness: 0.2, metalness: 0.9 }
  },
  {
    id: 'fan_spreader',
    name: '扇形布料机',
    path: '/models/扇形布料机ok.glb',
    scale: 0.01,
    defaultMaterial: { color: 0x0f6c02, roughness: 0.2, metalness: 0.9 }
  },
  {
    id: 'excavator_3500t',
    name: '3500T斗轮挖掘机',
    path: '/models/3500T斗轮挖掘机.glb',
    scale: 0.01,
    defaultMaterial: { color: 0x02536c, roughness: 0.2, metalness: 0.9 }
  },
  {
    id: 'excavator_1500t',
    name: '1500T斗轮挖掘机',
    path: '/models/1500T斗轮挖掘机.glb',
    scale: 0.01,
    defaultMaterial: { color: 0x02536c, roughness: 0.3, metalness: 0.8 }
  },
  {
    id: 'mobile_loader',
    name: '移动转载机',
    path: '/models/移动转载机ok.glb',
    scale: 0.01,
    defaultMaterial: { color: 0x02536c, roughness: 0.2, metalness: 0.9 }
  }
];

// 表单数据
const selectedDeviceType = ref<string>('');
const deviceName = ref<string>('');
const position = ref({ x: 0, y: 0, z: 0 });
const rotation = ref({ x: 0, y: 0, z: 0 });
const customMaterial = ref({
  color: '#02536c',
  roughness: 0.2,
  metalness: 0.9
});

// 临时设备列表
const tempDevices = ref<DeviceConfig[]>([]);

// 计算属性
const selectedType = computed(() => 
  deviceTypes.find(type => type.id === selectedDeviceType.value)
);

// 生成唯一ID
const generateDeviceId = () => {
  const timestamp = Date.now();
  const random = Math.floor(Math.random() * 1000);
  return `device_${timestamp}_${random}`;
};

// 重置表单
const resetForm = () => {
  selectedDeviceType.value = '';
  deviceName.value = '';
  position.value = { x: 0, y: 0, z: 0 };
  rotation.value = { x: 0, y: 0, z: 0 };
  if (selectedType.value) {
    const hexColor = `#${selectedType.value.defaultMaterial.color.toString(16).padStart(6, '0')}`;
    customMaterial.value = {
      color: hexColor,
      roughness: selectedType.value.defaultMaterial.roughness,
      metalness: selectedType.value.defaultMaterial.metalness
    };
  }
};

// 监听设备类型变化，自动填充默认值
watch(selectedDeviceType, (newType) => {
  if (newType) {
    const type = deviceTypes.find(t => t.id === newType);
    if (type) {
      deviceName.value = type.name;
      const hexColor = `#${type.defaultMaterial.color.toString(16).padStart(6, '0')}`;
      customMaterial.value = {
        color: hexColor,
        roughness: type.defaultMaterial.roughness,
        metalness: type.defaultMaterial.metalness
      };
    }
  }
});

// 添加设备到场景
const addDeviceToScene = () => {
  console.log('DeviceManager - 开始添加设备');

  if (!selectedType.value || !deviceName.value.trim()) {
    alert('请选择设备类型并输入设备名称');
    return;
  }

  const deviceId = generateDeviceId();

  // 转换颜色值
  const colorHex = customMaterial.value.color.replace('#', '');
  const colorValue = parseInt(colorHex, 16);

  const deviceConfig: DeviceConfig = {
    id: deviceId,
    name: deviceName.value.trim(),
    type: selectedType.value.id,
    path: selectedType.value.path,
    position: { ...position.value },
    rotation: { ...rotation.value },
    scale: selectedType.value.scale,
    material: {
      color: colorValue,
      roughness: customMaterial.value.roughness,
      metalness: customMaterial.value.metalness
    },
    battery: Math.floor(Math.random() * 40) + 60, // 随机电量 60-100%
    sensors: []
  };

  console.log('DeviceManager - 设备配置:', deviceConfig);

  // 添加到临时设备列表
  tempDevices.value.push(deviceConfig);

  // 发送事件给父组件，添加到场景中
  console.log('DeviceManager - 发送device-added事件');
  emit('device-added', deviceConfig);

  // 重置表单
  resetForm();

  console.log('DeviceManager - 设备已添加到临时列表，当前设备数量:', tempDevices.value.length);
};

// 从场景中移除设备
const removeDeviceFromScene = (deviceId: string) => {
  const index = tempDevices.value.findIndex(device => device.id === deviceId);
  if (index !== -1) {
    tempDevices.value.splice(index, 1);
    emit('device-removed', deviceId);
    console.log('设备已移除:', deviceId);
  }
};

// 导出设备配置
const exportDevicesConfig = () => {
  console.log('DeviceManager - 开始导出设备配置');
  console.log('DeviceManager - 当前设备数量:', tempDevices.value.length);

  if (tempDevices.value.length === 0) {
    alert('没有设备可以导出');
    return;
  }

  const config = {
    timestamp: new Date().toISOString(),
    devices: tempDevices.value,
    totalDevices: tempDevices.value.length
  };

  console.log('DeviceManager - 导出配置:', config);

  const jsonString = JSON.stringify(config, null, 2);
  const blob = new Blob([jsonString], { type: 'application/json' });
  const url = URL.createObjectURL(blob);
  const a = document.createElement('a');
  a.href = url;
  a.download = `devices-config-${Date.now()}.json`;
  a.click();
  URL.revokeObjectURL(url);

  emit('devices-exported', config);
  console.log('DeviceManager - 设备配置已导出');
};

// 导入设备配置
const importDevicesConfig = (event: Event) => {
  const target = event.target as HTMLInputElement;
  const file = target.files?.[0];
  if (!file) return;

  const reader = new FileReader();
  reader.onload = e => {
    try {
      const config = JSON.parse(e.target?.result as string);
      if (config.devices && Array.isArray(config.devices)) {
        // 清空当前设备
        tempDevices.value.forEach(device => {
          emit('device-removed', device.id);
        });
        tempDevices.value.splice(0, tempDevices.value.length);

        // 添加导入的设备
        config.devices.forEach((device: DeviceConfig) => {
          // 确保设备有必要的属性
          const validDevice = {
            ...device,
            id: device.id || generateDeviceId(),
            battery: device.battery || Math.floor(Math.random() * 40) + 60,
            sensors: device.sensors || []
          };

          tempDevices.value.push(validDevice);
          emit('device-added', validDevice);
        });

        emit('devices-imported', config);
        console.log('设备配置已导入:', config);
        alert(`成功导入 ${config.devices.length} 个设备`);
      } else {
        alert('无效的配置文件格式');
      }
    } catch (error) {
      console.error('导入配置失败:', error);
      alert('导入配置失败，请检查文件格式');
    }
  };
  reader.readAsText(file);

  // 重置文件输入
  target.value = '';
};

// 清空所有设备
const clearAllDevices = () => {
  console.log('DeviceManager - 开始清空所有设备');
  console.log('DeviceManager - 当前设备数量:', tempDevices.value.length);

  if (tempDevices.value.length === 0) {
    alert('当前没有设备需要清空');
    return;
  }

  if (confirm(`确定要清空所有 ${tempDevices.value.length} 个设备吗？`)) {
    const deviceIds = tempDevices.value.map(device => device.id);
    console.log('DeviceManager - 要清空的设备IDs:', deviceIds);

    tempDevices.value = [];

    // 通知父组件移除所有设备
    deviceIds.forEach(id => {
      console.log('DeviceManager - 发送device-removed事件:', id);
      emit('device-removed', id);
    });

    console.log('DeviceManager - 所有设备已清空');
  }
};
</script>

<template>
  <div
    class="futuristic-card-symmetric relative overflow-hidden group bg-gradient-to-br from-slate-800/80 to-slate-700/60 border border-cyan-500/30 shadow-lg shadow-cyan-500/10 hover:shadow-cyan-500/20 transition-all duration-300"
  >
    <!-- Card Glow Effect -->
    <div
      class="absolute inset-0 bg-gradient-to-br from-cyan-400/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 blur-md"
    ></div>

    <!-- Corner Decorations -->
    <div class="absolute top-2 left-2 w-4 h-4 border-l-2 border-t-2 border-cyan-400/60"></div>
    <div class="absolute top-2 right-2 w-4 h-4 border-r-2 border-t-2 border-cyan-400/60"></div>
    <div class="absolute bottom-2 left-2 w-4 h-4 border-l-2 border-b-2 border-cyan-400/60"></div>
    <div class="absolute bottom-2 right-2 w-4 h-4 border-r-2 border-b-2 border-cyan-400/60"></div>

    <div class="relative z-10">
      <div class="flex items-center gap-3 p-4 pb-2 border-b border-cyan-500/10">
        <div class="w-2 h-2 rounded-full shrink-0 bg-blue-500 shadow-lg shadow-blue-500/50"></div>
        <h3 class="text-sm font-semibold text-white m-0">设备管理</h3>
        <div class="ml-auto text-xs text-cyan-400">{{ tempDevices.length }} 个设备</div>
      </div>

      <!-- 配置管理按钮 -->
      <div class="p-4 border-b border-cyan-500/10">
        <div class="flex flex-col gap-2">
          <!-- 导入配置按钮 -->
          <label
            for="device-config-file"
            class="futuristic-btn-symmetric flex items-center justify-center px-3 py-2 text-xs font-medium transition-all duration-300 ease-in border bg-cyan-500/20 border-cyan-500 text-cyan-500 hover:bg-cyan-500/30 cursor-pointer relative overflow-hidden group"
          >
            <input
              type="file"
              id="device-config-file"
              accept=".json"
              @change="importDevicesConfig"
              class="hidden"
            />
            <span class="relative z-10 flex items-center">
              <svg class="w-3 h-3 mr-2" fill="currentColor" viewBox="0 0 24 24">
                <path d="M14 2H6c-1.1 0-2 .9-2 2v16c0 1.1.9 2 2 2h8c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2zm0 18H6V4h8v16z"/>
                <path d="M8 14h8v2H8v-2zm0-3h4v2H8v-2zm0-3h8v2H8v-2z"/>
              </svg>
              导入配置
            </span>
            <div class="absolute inset-0 bg-gradient-to-r from-cyan-400/30 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 blur-sm"></div>
          </label>

          <!-- 导出配置按钮 -->
          <button
            @click="exportDevicesConfig"
            :disabled="tempDevices.length === 0"
            class="futuristic-btn-symmetric flex items-center justify-center px-3 py-2 text-xs font-medium transition-all duration-300 ease-in border bg-orange-500/20 border-orange-500 text-orange-500 hover:bg-orange-500/30 disabled:opacity-50 disabled:cursor-not-allowed relative overflow-hidden group"
          >
            <span class="relative z-10 flex items-center">
              <svg class="w-3 h-3 mr-2" fill="currentColor" viewBox="0 0 24 24">
                <path d="M14 2H6c-1.1 0-2 .9-2 2v16c0 1.1.9 2 2 2h8c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2zm0 18H6V4h8v16z"/>
                <path d="M8 14h8v2H8v-2zm0-3h4v2H8v-2zm0-3h8v2H8v-2z"/>
              </svg>
              导出配置
            </span>
            <div class="absolute inset-0 bg-gradient-to-r from-orange-400/30 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 blur-sm"></div>
          </button>

          <!-- 清空设备按钮 -->
          <button
            @click="clearAllDevices"
            :disabled="tempDevices.length === 0"
            class="futuristic-btn-symmetric flex items-center justify-center px-3 py-2 text-xs font-medium transition-all duration-300 ease-in border bg-red-500/20 border-red-500 text-red-500 hover:bg-red-500/30 disabled:opacity-50 disabled:cursor-not-allowed relative overflow-hidden group"
          >
            <span class="relative z-10 flex items-center">
              <svg class="w-3 h-3 mr-2" fill="currentColor" viewBox="0 0 24 24">
                <path d="M6 19c0 1.1.9 2 2 2h8c1.1 0 2-.9 2-2V7H6v12zM19 4h-3.5l-1-1h-5l-1 1H5v2h14V4z"/>
              </svg>
              清空设备
            </span>
            <div class="absolute inset-0 bg-gradient-to-r from-red-400/30 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 blur-sm"></div>
          </button>
        </div>
      </div>

      <!-- 设备列表 -->
      <div class="p-4">
        <div v-if="tempDevices.length > 0">
          <div class="flex items-center justify-between mb-2">
            <label class="block text-xs text-white/70">当前设备列表</label>
          </div>
          <div class="max-h-40 overflow-y-auto space-y-2">
            <div
              v-for="device in tempDevices"
              :key="device.id"
              class="flex items-center justify-between p-2 bg-slate-700/40 border border-cyan-500/20 rounded text-xs"
            >
              <div class="flex-1 min-w-0">
                <div class="text-white font-medium truncate">{{ device.name }}</div>
                <div class="text-slate-400 text-xs">
                  {{ deviceTypes.find(t => t.id === device.type)?.name }}
                </div>
                <div class="text-cyan-400 text-xs">
                  ({{ device.position.x }}, {{ device.position.y }}, {{ device.position.z }})
                </div>
              </div>
              <button
                @click="removeDeviceFromScene(device.id)"
                class="ml-2 text-red-400 hover:text-red-300 transition-colors"
              >
                <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M6 18L18 6M6 6l12 12" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
                </svg>
              </button>
            </div>
          </div>
        </div>
        <div v-else class="text-center text-slate-400 text-xs py-4">
          暂无设备
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
/* Symmetric futuristic card style */
.futuristic-card-symmetric {
  clip-path: polygon(1.5rem 0, 100% 0, 100% calc(100% - 1.5rem), calc(100% - 1.5rem) 100%, 0 100%, 0 1.5rem);
}

/* Symmetric futuristic input style */
.futuristic-input-symmetric {
  clip-path: polygon(0.5rem 0, 100% 0, 100% calc(100% - 0.5rem), calc(100% - 0.5rem) 100%, 0 100%, 0 0.5rem);
}

/* Symmetric futuristic button style */
.futuristic-btn-symmetric {
  clip-path: polygon(0.75rem 0, 100% 0, 100% calc(100% - 0.75rem), calc(100% - 0.75rem) 100%, 0 100%, 0 0.75rem);
}

/* Custom scrollbar for device list */
.max-h-40::-webkit-scrollbar {
  width: 4px;
}

.max-h-40::-webkit-scrollbar-track {
  background: rgba(51, 65, 85, 0.3);
}

.max-h-40::-webkit-scrollbar-thumb {
  background: rgba(6, 182, 212, 0.5);
  border-radius: 2px;
}

.max-h-40::-webkit-scrollbar-thumb:hover {
  background: rgba(6, 182, 212, 0.7);
}
</style>
